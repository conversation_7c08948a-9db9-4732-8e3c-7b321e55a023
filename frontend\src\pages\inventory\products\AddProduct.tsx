import { useState } from "react"
import { useNavigate } from "react-router-dom"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
    Select,
    SelectItem,
} from "@/components/ui/select"
import { ArrowLeft, Save, X } from "lucide-react"
import { useGlobalAlert } from "@/components/common/GlobalAlert"

interface ProductFormData {
    name: string
    category: string
    brand: string
    description: string
    price: string
    costPrice: string
    stock: string
    minStock: string
    unit: string
    barcode: string
    supplier: string
}

const categories = [
    "Grains",
    "Spices",
    "Dairy",
    "Instant Food",
    "Snacks",
    "Beverages",
    "Personal Care",
    "Household",
    "Fruits & Vegetables",
    "Frozen Foods"
]

const units = [
    "kg",
    "gm",
    "ltr",
    "ml",
    "pcs",
    "pack",
    "box",
    "bottle"
]

export default function AddProduct() {
    const navigate = useNavigate()
    const alert = useGlobalAlert()
    const [isLoading, setIsLoading] = useState(false)
    const [errors, setErrors] = useState<Partial<ProductFormData>>({})
    const [formData, setFormData] = useState<ProductFormData>({
        name: "",
        category: "",
        brand: "",
        description: "",
        price: "",
        costPrice: "",
        stock: "",
        minStock: "",
        unit: "",
        barcode: "",
        supplier: ""
    })

    const handleInputChange = (field: keyof ProductFormData, value: string) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }))
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: undefined
            }))
        }
    }

    const validateForm = (): boolean => {
        const newErrors: Partial<ProductFormData> = {}

        if (!formData.name.trim()) {
            newErrors.name = "Product name is required"
        }
        if (!formData.category) {
            newErrors.category = "Category is required"
        }
        if (!formData.price || parseFloat(formData.price) <= 0) {
            newErrors.price = "Valid selling price is required"
        }
        if (!formData.stock || parseInt(formData.stock) < 0) {
            newErrors.stock = "Valid stock quantity is required"
        }
        if (!formData.unit) {
            newErrors.unit = "Unit is required"
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!validateForm()) {
            return
        }

        setIsLoading(true)

        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000))

            // Here you would typically make an API call to save the product
            console.log("Product data:", formData)

            // Show success alert
            alert.showSuccess(`Product "${formData.name}" added successfully!`)

            // Navigate back to inventory after successful save
            navigate("/inventory")
        } catch (error) {
            console.error("Error saving product:", error)
            // Show error alert
            alert.showError("Failed to save product. Please try again.")
        } finally {
            setIsLoading(false)
        }
    }

    const ErrorMessage = ({ error }: { error?: string }) => {
        if (!error) return null
        return <p className="text-sm text-red-600 mt-1">{error}</p>
    }

    return (
        <div className="flex flex-1 flex-col gap-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigate("/inventory")}
                    className="flex items-center gap-2 border"
                >
                    <ArrowLeft className="h-4 w-4" />
                    Back
                </Button>
                <div>
                    <h1 className="text-2xl font-bold">Add New Product</h1>
                    <p className="text-muted-foreground">
                        Add a new product to your inventory
                    </p>
                </div>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit}>
                <Card>
                    <CardHeader>
                        <CardTitle>Product Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {/* Basic Information */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="name">Product Name *</Label>
                                <Input
                                    id="name"
                                    placeholder="Enter product name"
                                    value={formData.name}
                                    onChange={(e) => handleInputChange("name", e.target.value)}
                                    className={errors.name ? "border-red-500" : ""}
                                    required
                                />
                                <ErrorMessage error={errors.name} />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="category">Category *</Label>
                                <Select
                                    id="category"
                                    value={formData.category}
                                    onValueChange={(value) => handleInputChange("category", value)}
                                    placeholder="Select category"
                                    className={errors.category ? "border-red-500" : ""}
                                    required
                                >
                                    {categories.map((category) => (
                                        <SelectItem key={category} value={category}>
                                            {category}
                                        </SelectItem>
                                    ))}
                                </Select>
                                <ErrorMessage error={errors.category} />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="brand">Brand *</Label>
                                <Select
                                    id="brand"
                                    value={formData.brand}
                                    onValueChange={(value) => handleInputChange("brand", value)}
                                    placeholder="Select brand"
                                    className={errors.brand ? "border-red-500" : ""}
                                    required
                                >
                                    {categories.map((category) => (
                                        <SelectItem key={category} value={category}>
                                            {category}
                                        </SelectItem>
                                    ))}
                                </Select>
                                <ErrorMessage error={errors.brand} />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="description">Description</Label>
                            <Textarea
                                id="description"
                                placeholder="Enter product description"
                                value={formData.description}
                                onChange={(e) => handleInputChange("description", e.target.value)}
                                rows={3}
                            />
                        </div>

                        {/* Pricing */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="price">Selling Price (₹) *</Label>
                                <Input
                                    id="price"
                                    type="number"
                                    step="0.01"
                                    placeholder="0.00"
                                    value={formData.price}
                                    onChange={(e) => handleInputChange("price", e.target.value)}
                                    className={errors.price ? "border-red-500" : ""}
                                    required
                                />
                                <ErrorMessage error={errors.price} />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="costPrice">Cost Price (₹)</Label>
                                <Input
                                    id="costPrice"
                                    type="number"
                                    step="0.01"
                                    placeholder="0.00"
                                    value={formData.costPrice}
                                    onChange={(e) => handleInputChange("costPrice", e.target.value)}
                                />
                            </div>
                        </div>

                        {/* Stock Information */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="stock">Current Stock *</Label>
                                <Input
                                    id="stock"
                                    type="number"
                                    placeholder="0"
                                    value={formData.stock}
                                    onChange={(e) => handleInputChange("stock", e.target.value)}
                                    className={errors.stock ? "border-red-500" : ""}
                                    required
                                />
                                <ErrorMessage error={errors.stock} />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="minStock">Minimum Stock</Label>
                                <Input
                                    id="minStock"
                                    type="number"
                                    placeholder="0"
                                    value={formData.minStock}
                                    onChange={(e) => handleInputChange("minStock", e.target.value)}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="unit">Unit *</Label>
                                <Select
                                    id="unit"
                                    value={formData.unit}
                                    onValueChange={(value) => handleInputChange("unit", value)}
                                    placeholder="Select unit"
                                    className={errors.unit ? "border-red-500" : ""}
                                    required
                                >
                                    {units.map((unit) => (
                                        <SelectItem key={unit} value={unit}>
                                            {unit}
                                        </SelectItem>
                                    ))}
                                </Select>
                                <ErrorMessage error={errors.unit} />
                            </div>
                        </div>

                        {/* Additional Information */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="barcode">Barcode/SKU</Label>
                                <Input
                                    id="barcode"
                                    placeholder="Enter barcode or SKU"
                                    value={formData.barcode}
                                    onChange={(e) => handleInputChange("barcode", e.target.value)}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="supplier">Supplier</Label>
                                <Input
                                    id="supplier"
                                    placeholder="Enter supplier name"
                                    value={formData.supplier}
                                    onChange={(e) => handleInputChange("supplier", e.target.value)}
                                />
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex justify-end gap-3 pt-6">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => navigate("/inventory")}
                                disabled={isLoading}
                            >
                                <X className="h-4 w-4 mr-2" />
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={isLoading}
                            >
                                <Save className="h-4 w-4 mr-2" />
                                {isLoading ? "Saving..." : "Save"}
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </form>
        </div>
    )
}