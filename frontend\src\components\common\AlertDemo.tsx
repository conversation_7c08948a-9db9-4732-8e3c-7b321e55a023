import React from "react"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { GlobalAlert, useAlert, useGlobalAlert } from "./GlobalAlert"

function AlertDemo() {
    // Method 1: Using the hook directly (local state)
    const { alert, showSuccess, showWarning, showError, showInfo, hideAlert } = useAlert()

    // Method 2: Using global context (if wrapped in AlertProvider)
    const globalAlert = useGlobalAlert()

    return (
        <div className="space-y-6">
            <Card>
                <CardHeader>
                    <CardTitle>Global Alert Component Demo</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                    {/* Enhanced Static Examples */}
                    <div className="space-y-6">
                        <h3 className="text-lg font-semibold">Enhanced Alert Examples</h3>

                        <div className="space-y-4">
                            <GlobalAlert
                                message="🎉 Product added successfully! Your inventory has been updated."
                                type="success"
                                showCloseButton={false}
                                className="animate-bounce-in"
                            />

                            <GlobalAlert
                                message="⚠️ Stock is running low! Consider restocking soon to avoid shortages."
                                type="warning"
                                showCloseButton={false}
                                className="animate-fade-in-up"
                            />

                            <GlobalAlert
                                message="❌ Failed to save data. Please check your connection and try again."
                                type="error"
                                showCloseButton={false}
                                className="animate-slide-in-top"
                            />

                            <GlobalAlert
                                message="💡 New inventory features are now available! Check out the latest updates."
                                type="info"
                                showCloseButton={false}
                                className="alert-hover-lift"
                            />
                        </div>

                        <div className="space-y-4">
                            <h4 className="font-medium">With Progress Bars</h4>

                            <GlobalAlert
                                message="Auto-dismissing success alert with progress indicator"
                                type="success"
                                showProgress={true}
                                duration={8000}
                                onClose={() => console.log("Success alert closed")}
                            />

                            <GlobalAlert
                                message="Warning alert with countdown timer"
                                type="warning"
                                showProgress={true}
                                duration={6000}
                                onClose={() => console.log("Warning alert closed")}
                            />
                        </div>
                    </div>

                    {/* Interactive Examples with Local State */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Interactive Alerts (Local State)</h3>
                        
                        <div className="grid grid-cols-2 gap-3">
                            <Button
                                onClick={() => showSuccess("🎉 Product added successfully with enhanced styling!")}
                                className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                                ✨ Enhanced Success
                            </Button>

                            <Button
                                onClick={() => showWarning("⚠️ Stock is critically low! Immediate action required.")}
                                className="bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                                🔔 Enhanced Warning
                            </Button>

                            <Button
                                onClick={() => showError("❌ Critical error occurred! Please contact support immediately.")}
                                className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                                🚨 Enhanced Error
                            </Button>

                            <Button
                                onClick={() => showInfo("💡 Amazing new features are now live! Explore the enhanced inventory system.")}
                                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                                🌟 Enhanced Info
                            </Button>
                        </div>

                        {/* Display local alert */}
                        {alert && (
                            <GlobalAlert
                                message={alert.message}
                                type={alert.type}
                                onClose={hideAlert}
                            />
                        )}
                    </div>

                    {/* Interactive Examples with Global Context */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Global Context Alerts (Fixed Position)</h3>
                        
                        <div className="grid grid-cols-2 gap-3">
                            <Button
                                onClick={() => globalAlert.showSuccess("🌟 Global success with progress bar and enhanced animations!")}
                                variant="outline"
                                className="border-green-600 text-green-600 hover:bg-green-50 hover:border-green-700 transition-all duration-300 hover:shadow-lg"
                            >
                                🎯 Global Success
                            </Button>

                            <Button
                                onClick={() => globalAlert.showWarning("⚡ Global warning with shimmer effects and smooth animations!")}
                                variant="outline"
                                className="border-orange-600 text-orange-600 hover:bg-orange-50 hover:border-orange-700 transition-all duration-300 hover:shadow-lg"
                            >
                                ⚡ Global Warning
                            </Button>

                            <Button
                                onClick={() => globalAlert.showError("🔥 Global error with enhanced styling and hover effects!")}
                                variant="outline"
                                className="border-red-600 text-red-600 hover:bg-red-50 hover:border-red-700 transition-all duration-300 hover:shadow-lg"
                            >
                                🔥 Global Error
                            </Button>

                            <Button
                                onClick={() => globalAlert.showInfo("✨ Global info with beautiful gradients and animations!")}
                                variant="outline"
                                className="border-blue-600 text-blue-600 hover:bg-blue-50 hover:border-blue-700 transition-all duration-300 hover:shadow-lg"
                            >
                                ✨ Global Info
                            </Button>
                        </div>
                    </div>

                    {/* Usage Examples */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Usage Examples</h3>
                        
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium mb-2">Method 1: Direct Component Usage</h4>
                            <pre className="text-sm text-gray-700 overflow-x-auto">
{`<GlobalAlert 
  message="Operation completed successfully!" 
  type="success" 
  onClose={() => setShowAlert(false)}
/>`}
                            </pre>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium mb-2">Method 2: Using Hook (Local State)</h4>
                            <pre className="text-sm text-gray-700 overflow-x-auto">
{`const { showSuccess, showError } = useAlert()

// Show alerts
showSuccess("Product saved!")
showError("Failed to save product")`}
                            </pre>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium mb-2">Method 3: Global Context (Recommended)</h4>
                            <pre className="text-sm text-gray-700 overflow-x-auto">
{`// Wrap your app with AlertProvider
<AlertProvider>
  <App />
</AlertProvider>

// Use anywhere in your app
const alert = useGlobalAlert()
alert.showSuccess("Global success message!")`}
                            </pre>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}

export default AlertDemo
