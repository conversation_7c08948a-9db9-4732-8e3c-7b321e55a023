
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { GlobalAlert, useGlobalAlert } from "./GlobalAlert"

function AlertDemo() {
    // Using global context (wrapped in AlertProvider)
    const globalAlert = useGlobalAlert()

    return (
        <div className="space-y-6">
            <Card>
                <CardHeader>
                    <CardTitle>Global Alert Component Demo</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                    {/* Enhanced Static Examples */}
                    <div className="space-y-6">
                        <h3 className="text-lg font-semibold">Enhanced Alert Examples</h3>

                        <div className="space-y-4">
                            <GlobalAlert
                                message="🎉 Product added successfully! Your inventory has been updated."
                                type="success"
                                showCloseButton={false}
                                className="animate-bounce-in"
                            />

                            <GlobalAlert
                                message="⚠️ Stock is running low! Consider restocking soon to avoid shortages."
                                type="warning"
                                showCloseButton={false}
                                className="animate-fade-in-up"
                            />

                            <GlobalAlert
                                message="❌ Failed to save data. Please check your connection and try again."
                                type="error"
                                showCloseButton={false}
                                className="animate-slide-in-top"
                            />

                            <GlobalAlert
                                message="💡 New inventory features are now available! Check out the latest updates."
                                type="info"
                                showCloseButton={false}
                                className="alert-hover-lift"
                            />
                        </div>

                        <div className="space-y-4">
                            <h4 className="font-medium">With Progress Bars</h4>

                            <GlobalAlert
                                message="Auto-dismissing success alert with progress indicator"
                                type="success"
                                showProgress={true}
                                duration={8000}
                                onClose={() => console.log("Success alert closed")}
                            />

                            <GlobalAlert
                                message="Warning alert with countdown timer"
                                type="warning"
                                showProgress={true}
                                duration={6000}
                                onClose={() => console.log("Warning alert closed")}
                            />
                        </div>
                    </div>

                    {/* Interactive Examples with Global Context */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Interactive Global Alerts</h3>

                        <div className="grid grid-cols-2 gap-3">
                            <Button
                                onClick={() => globalAlert.showSuccess("🎉 Product added successfully with enhanced styling!")}
                                className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                                ✨ Enhanced Success
                            </Button>

                            <Button
                                onClick={() => globalAlert.showWarning("⚠️ Stock is critically low! Immediate action required.")}
                                className="bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                                🔔 Enhanced Warning
                            </Button>

                            <Button
                                onClick={() => globalAlert.showError("❌ Critical error occurred! Please contact support immediately.")}
                                className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                                🚨 Enhanced Error
                            </Button>

                            <Button
                                onClick={() => globalAlert.showInfo("💡 Amazing new features are now live! Explore the enhanced inventory system.")}
                                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                                🌟 Enhanced Info
                            </Button>
                        </div>
                    </div>

                    {/* Additional Global Examples */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Additional Global Alert Examples</h3>
                        
                        <div className="grid grid-cols-2 gap-3">
                            <Button
                                onClick={() => globalAlert.showSuccess("🌟 Global success with progress bar and enhanced animations!")}
                                variant="outline"
                                className="border-green-600 text-green-600 hover:bg-green-50 hover:border-green-700 transition-all duration-300 hover:shadow-lg"
                            >
                                🎯 Global Success
                            </Button>

                            <Button
                                onClick={() => globalAlert.showWarning("⚡ Global warning with shimmer effects and smooth animations!")}
                                variant="outline"
                                className="border-orange-600 text-orange-600 hover:bg-orange-50 hover:border-orange-700 transition-all duration-300 hover:shadow-lg"
                            >
                                ⚡ Global Warning
                            </Button>

                            <Button
                                onClick={() => globalAlert.showError("🔥 Global error with enhanced styling and hover effects!")}
                                variant="outline"
                                className="border-red-600 text-red-600 hover:bg-red-50 hover:border-red-700 transition-all duration-300 hover:shadow-lg"
                            >
                                🔥 Global Error
                            </Button>

                            <Button
                                onClick={() => globalAlert.showInfo("✨ Global info with beautiful gradients and animations!")}
                                variant="outline"
                                className="border-blue-600 text-blue-600 hover:bg-blue-50 hover:border-blue-700 transition-all duration-300 hover:shadow-lg"
                            >
                                ✨ Global Info
                            </Button>
                        </div>
                    </div>

                    {/* Usage Examples */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Usage Examples</h3>
                        
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium mb-2">Method 1: Direct Component Usage</h4>
                            <pre className="text-sm text-gray-700 overflow-x-auto">
{`<GlobalAlert 
  message="Operation completed successfully!" 
  type="success" 
  onClose={() => setShowAlert(false)}
/>`}
                            </pre>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium mb-2">Method 2: Global Context (Recommended)</h4>
                            <pre className="text-sm text-gray-700 overflow-x-auto">
{`// Wrap your app with AlertProvider (already done in AppLayout)
<AlertProvider>
  <App />
</AlertProvider>

// Use anywhere in your app
const alert = useGlobalAlert()
alert.showSuccess("Global success message!")
alert.showError("Failed to save product")
alert.showWarning("Please check your input")
alert.showInfo("New features available")`}
                            </pre>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium mb-2">Method 3: Enhanced Features</h4>
                            <pre className="text-sm text-gray-700 overflow-x-auto">
{`// With progress bar and custom duration
<GlobalAlert
  message="Auto-dismissing alert"
  type="success"
  showProgress={true}
  duration={8000}
  onClose={() => console.log("Closed")}
/>

// With custom animations
<GlobalAlert
  message="Animated alert"
  type="success"
  className="animate-bounce-in"
/>`}
                            </pre>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}

export default AlertDemo
