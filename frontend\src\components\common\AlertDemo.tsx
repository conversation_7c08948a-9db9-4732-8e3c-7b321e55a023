import React from "react"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { GlobalAlert, useAlert, useGlobalAlert } from "./GlobalAlert"

function AlertDemo() {
    // Method 1: Using the hook directly (local state)
    const { alert, showSuccess, showWarning, showError, showInfo, hideAlert } = useAlert()

    // Method 2: Using global context (if wrapped in AlertProvider)
    const globalAlert = useGlobalAlert()

    return (
        <div className="space-y-6">
            <Card>
                <CardHeader>
                    <CardTitle>Global Alert Component Demo</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                    {/* Static Examples */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Static Alert Examples</h3>
                        
                        <GlobalAlert 
                            message="Operation completed successfully!" 
                            type="success" 
                            showCloseButton={false}
                        />
                        
                        <GlobalAlert 
                            message="Please check your input data before proceeding." 
                            type="warning" 
                            showCloseButton={false}
                        />
                        
                        <GlobalAlert 
                            message="Failed to save data. Please try again." 
                            type="error" 
                            showCloseButton={false}
                        />
                        
                        <GlobalAlert 
                            message="New features are available in the latest update." 
                            type="info" 
                            showCloseButton={false}
                        />
                    </div>

                    {/* Interactive Examples with Local State */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Interactive Alerts (Local State)</h3>
                        
                        <div className="flex gap-2 flex-wrap">
                            <Button 
                                onClick={() => showSuccess("Product added successfully!")}
                                className="bg-green-600 hover:bg-green-700"
                            >
                                Show Success
                            </Button>
                            
                            <Button 
                                onClick={() => showWarning("Stock is running low!")}
                                className="bg-orange-600 hover:bg-orange-700"
                            >
                                Show Warning
                            </Button>
                            
                            <Button 
                                onClick={() => showError("Failed to delete product!")}
                                className="bg-red-600 hover:bg-red-700"
                            >
                                Show Error
                            </Button>
                            
                            <Button 
                                onClick={() => showInfo("System maintenance scheduled for tonight.")}
                                className="bg-blue-600 hover:bg-blue-700"
                            >
                                Show Info
                            </Button>
                        </div>

                        {/* Display local alert */}
                        {alert && (
                            <GlobalAlert
                                message={alert.message}
                                type={alert.type}
                                onClose={hideAlert}
                            />
                        )}
                    </div>

                    {/* Interactive Examples with Global Context */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Global Context Alerts (Fixed Position)</h3>
                        
                        <div className="flex gap-2 flex-wrap">
                            <Button 
                                onClick={() => globalAlert.showSuccess("Global success alert!")}
                                variant="outline"
                                className="border-green-600 text-green-600 hover:bg-green-50"
                            >
                                Global Success
                            </Button>
                            
                            <Button 
                                onClick={() => globalAlert.showWarning("Global warning alert!")}
                                variant="outline"
                                className="border-orange-600 text-orange-600 hover:bg-orange-50"
                            >
                                Global Warning
                            </Button>
                            
                            <Button 
                                onClick={() => globalAlert.showError("Global error alert!")}
                                variant="outline"
                                className="border-red-600 text-red-600 hover:bg-red-50"
                            >
                                Global Error
                            </Button>
                            
                            <Button 
                                onClick={() => globalAlert.showInfo("Global info alert!")}
                                variant="outline"
                                className="border-blue-600 text-blue-600 hover:bg-blue-50"
                            >
                                Global Info
                            </Button>
                        </div>
                    </div>

                    {/* Usage Examples */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Usage Examples</h3>
                        
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium mb-2">Method 1: Direct Component Usage</h4>
                            <pre className="text-sm text-gray-700 overflow-x-auto">
{`<GlobalAlert 
  message="Operation completed successfully!" 
  type="success" 
  onClose={() => setShowAlert(false)}
/>`}
                            </pre>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium mb-2">Method 2: Using Hook (Local State)</h4>
                            <pre className="text-sm text-gray-700 overflow-x-auto">
{`const { showSuccess, showError } = useAlert()

// Show alerts
showSuccess("Product saved!")
showError("Failed to save product")`}
                            </pre>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h4 className="font-medium mb-2">Method 3: Global Context (Recommended)</h4>
                            <pre className="text-sm text-gray-700 overflow-x-auto">
{`// Wrap your app with AlertProvider
<AlertProvider>
  <App />
</AlertProvider>

// Use anywhere in your app
const alert = useGlobalAlert()
alert.showSuccess("Global success message!")`}
                            </pre>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}

export default AlertDemo
