import React from "react"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { 
    CheckCircle, 
    AlertTriangle, 
    XCircle, 
    Info, 
    X 
} from "lucide-react"
import { cn } from "@/lib/utils"

export type AlertType = "success" | "warning" | "error" | "info"

interface GlobalAlertProps {
    message: string
    type: AlertType
    onClose?: () => void
    className?: string
    showCloseButton?: boolean
}

const alertConfig = {
    success: {
        icon: CheckCircle,
        className: "border-green-500/50 text-green-700 bg-green-50 dark:border-green-500 dark:text-green-400 dark:bg-green-950/50",
        iconClassName: "text-green-600 dark:text-green-400"
    },
    warning: {
        icon: AlertTriangle,
        className: "border-orange-500/50 text-orange-700 bg-orange-50 dark:border-orange-500 dark:text-orange-400 dark:bg-orange-950/50",
        iconClassName: "text-orange-600 dark:text-orange-400"
    },
    error: {
        icon: XCircle,
        className: "border-red-500/50 text-red-700 bg-red-50 dark:border-red-500 dark:text-red-400 dark:bg-red-950/50",
        iconClassName: "text-red-600 dark:text-red-400"
    },
    info: {
        icon: Info,
        className: "border-blue-500/50 text-blue-700 bg-blue-50 dark:border-blue-500 dark:text-blue-400 dark:bg-blue-950/50",
        iconClassName: "text-blue-600 dark:text-blue-400"
    }
}

export function GlobalAlert({ 
    message, 
    type, 
    onClose, 
    className,
    showCloseButton = true 
}: GlobalAlertProps) {
    const config = alertConfig[type]
    const IconComponent = config.icon

    return (
        <Alert className={cn(config.className, className)}>
            <IconComponent className={cn("h-4 w-4", config.iconClassName)} />
            <AlertDescription className="flex items-center justify-between">
                <span>{message}</span>
                {showCloseButton && onClose && (
                    <button
                        onClick={onClose}
                        className={cn(
                            "ml-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
                            config.iconClassName
                        )}
                    >
                        <X className="h-4 w-4" />
                        <span className="sr-only">Close</span>
                    </button>
                )}
            </AlertDescription>
        </Alert>
    )
}

// Hook for managing alert state
export function useAlert() {
    const [alert, setAlert] = React.useState<{
        message: string
        type: AlertType
        id: string
    } | null>(null)

    const showAlert = React.useCallback((message: string, type: AlertType, duration: number = 5000) => {
        const id = Math.random().toString(36).substr(2, 9)
        setAlert({ message, type, id })

        if (duration > 0) {
            setTimeout(() => {
                setAlert(current => current?.id === id ? null : current)
            }, duration)
        }
    }, [])

    const hideAlert = React.useCallback(() => {
        setAlert(null)
    }, [])

    const showSuccess = React.useCallback((message: string, duration?: number) => {
        showAlert(message, "success", duration)
    }, [showAlert])

    const showWarning = React.useCallback((message: string, duration?: number) => {
        showAlert(message, "warning", duration)
    }, [showAlert])

    const showError = React.useCallback((message: string, duration?: number) => {
        showAlert(message, "error", duration)
    }, [showAlert])

    const showInfo = React.useCallback((message: string, duration?: number) => {
        showAlert(message, "info", duration)
    }, [showAlert])

    return {
        alert,
        showAlert,
        hideAlert,
        showSuccess,
        showWarning,
        showError,
        showInfo
    }
}

// Context for global alert management
interface AlertContextType {
    showAlert: (message: string, type: AlertType, duration?: number) => void
    showSuccess: (message: string, duration?: number) => void
    showWarning: (message: string, duration?: number) => void
    showError: (message: string, duration?: number) => void
    showInfo: (message: string, duration?: number) => void
}

const AlertContext = React.createContext<AlertContextType | undefined>(undefined)

export function AlertProvider({ children }: { children: React.ReactNode }) {
    const { alert, hideAlert, showAlert, showSuccess, showWarning, showError, showInfo } = useAlert()

    return (
        <AlertContext.Provider value={{ showAlert, showSuccess, showWarning, showError, showInfo }}>
            {children}
            {alert && (
                <div className="fixed top-4 right-4 z-50 w-full max-w-md">
                    <GlobalAlert
                        message={alert.message}
                        type={alert.type}
                        onClose={hideAlert}
                    />
                </div>
            )}
        </AlertContext.Provider>
    )
}

export function useGlobalAlert() {
    const context = React.useContext(AlertContext)
    if (context === undefined) {
        throw new Error('useGlobalAlert must be used within an AlertProvider')
    }
    return context
}

// Utility function for quick alerts without context
export function createAlert(message: string, type: AlertType) {
    return <GlobalAlert message={message} type={type} />
}
